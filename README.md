# Trina Admin I18n - 国际化管理系统

## 项目简介

Trina Admin I18n 是一个基于 Spring Boot 的国际化管理系统，参考 BallCat 的国际化实现方案，提供了完整的前后端国际化资源统一管理解决方案。

## 核心功能

### 1. 统一管理
- 支持不同业务应用的前后端资源国际化统一管理
- 基于数据库的国际化配置存储，支持动态修改

### 2. 管理页面
- 提供完整的Web管理界面
- 支持新增、修改、删除、查询基础功能
- 支持Excel导入导出功能
- 与后端一起部署，简化管理部署

### 3. 高性能缓存
- 实现两级缓存：Caffeine本地缓存 + Redis分布式缓存
- 支持缓存预热和失效策略
- 提供缓存统计和管理功能

### 4. 多应用支持
- 支持前端脚手架独立分应用集成
- 提供RESTful API接口
- 支持多语言环境切换

## 技术栈

### 后端技术
- **Spring Boot 3.5.4** - 主框架
- **MyBatis Plus 3.5.5** - ORM框架
- **MySQL 8.0** - 数据库
- **Redis** - 分布式缓存
- **Caffeine** - 本地缓存
- **Apache POI** - Excel处理
- **Thymeleaf** - 模板引擎

### 前端技术
- **Bootstrap 5.1.3** - UI框架
- **Bootstrap Icons** - 图标库
- **原生JavaScript** - 前端交互

## 快速开始

### 1. 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+
- Maven 3.6+

### 2. 数据库初始化
```sql
-- 执行数据库初始化脚本
source src/main/resources/db/schema.sql
```

### 3. 配置文件
修改 `src/main/resources/application.properties` 中的数据库和Redis连接配置：

```properties
# 数据库配置
spring.datasource.url=***********************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=your_password

# Redis配置
spring.data.redis.host=localhost
spring.data.redis.port=6379
spring.data.redis.password=your_password
```

### 4. 启动应用
```bash
mvn spring-boot:run
```

### 5. 访问系统
- 管理界面：http://localhost:8080/i18n
- 缓存管理：http://localhost:8080/i18n/cache
- API文档：http://localhost:8080/api/i18n

## API接口

### 基础CRUD接口
- `GET /api/i18n/page` - 分页查询
- `GET /api/i18n/{id}` - 根据ID查询
- `POST /api/i18n` - 新增
- `PUT /api/i18n/{id}` - 更新
- `DELETE /api/i18n/{id}` - 删除
- `DELETE /api/i18n/batch` - 批量删除

### 国际化服务接口
- `GET /api/i18n/message` - 获取国际化消息
- `GET /api/i18n/list` - 根据应用和语言获取数据
- `GET /api/i18n/applications` - 获取所有应用名称
- `GET /api/i18n/languages` - 获取所有语言标签

### 导入导出接口
- `POST /api/i18n/export` - 导出Excel
- `POST /api/i18n/import` - 导入Excel

### 缓存管理接口
- `POST /api/i18n/cache/refresh` - 刷新缓存
- `GET /api/i18n/cache/stats` - 获取缓存统计

## 数据库设计

### i18n_data 表结构
```sql
CREATE TABLE `i18n_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `application_name` varchar(50) NOT NULL DEFAULT '' COMMENT '应用名称',
  `language_tag` varchar(10) NOT NULL COMMENT '语言标签',
  `code` varchar(200) NOT NULL COMMENT '国际化唯一标识',
  `message` text COMMENT '国际化文本内容',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_lang_code` (`application_name`, `language_tag`, `code`, `deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='国际化数据表';
```

## 缓存架构

### 两级缓存设计
1. **本地缓存（Caffeine）**
   - 最大条目数：1000
   - 过期时间：30分钟
   - 高性能读取，减少网络开销

2. **分布式缓存（Redis）**
   - 键前缀：`trina:i18n:`
   - 过期时间：30分钟
   - 支持集群部署，数据共享

### 缓存策略
- **读取策略**：本地缓存 → Redis缓存 → 数据库
- **更新策略**：数据库更新后，同时更新两级缓存
- **失效策略**：支持按应用、按键精确失效

## 前端集成

### JavaScript集成示例
```javascript
// 获取国际化消息
async function getI18nMessage(applicationName, languageTag, code) {
    const response = await fetch(`/api/i18n/message?applicationName=${applicationName}&languageTag=${languageTag}&code=${code}`);
    const result = await response.json();
    return result.data;
}

// 获取应用的所有国际化数据
async function getI18nData(applicationName, languageTag) {
    const response = await fetch(`/api/i18n/list?applicationName=${applicationName}&languageTag=${languageTag}`);
    const result = await response.json();
    return result.data;
}
```

### Vue.js集成示例
```javascript
// 创建国际化插件
const i18nPlugin = {
    install(app, options) {
        app.config.globalProperties.$t = async (code, args = []) => {
            const message = await getI18nMessage(options.applicationName, options.languageTag, code);
            return formatMessage(message, args);
        };
    }
};

// 使用插件
app.use(i18nPlugin, {
    applicationName: 'your-app',
    languageTag: 'zh-CN'
});
```

## 部署说明

### Docker部署
```dockerfile
FROM openjdk:17-jdk-slim
COPY target/trina-admin-i18n-0.0.1-SNAPSHOT.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    environment:
      - SPRING_DATASOURCE_URL=**********************************
      - SPRING_DATA_REDIS_HOST=redis

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: trina_i18n

  redis:
    image: redis:6.2-alpine
```

## 开发指南

### 添加新的国际化配置
1. 通过管理界面添加
2. 通过API接口添加
3. 通过Excel批量导入

### 扩展新的应用
1. 在数据库中添加对应应用的国际化配置
2. 前端调用API获取国际化数据
3. 实现前端国际化切换逻辑

### 自定义缓存策略
1. 继承 `I18nCacheManager` 类
2. 重写缓存方法
3. 注册为Spring Bean

## 许可证

本项目采用 MIT 许可证，详情请参阅 [LICENSE](LICENSE) 文件。

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱：<EMAIL>
- GitHub Issues：https://github.com/your-repo/trina-admin-i18n/issues
