package com.trinasolar.admin.i18n.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.trinasolar.admin.i18n.entity.I18nData;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 国际化缓存管理器
 * 实现两级缓存：Caffeine本地缓存 + Redis分布式缓存
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class I18nCacheManager {

    private static final String REDIS_KEY_PREFIX = "trina:i18n:";
    private static final Duration REDIS_EXPIRE_TIME = Duration.ofMinutes(30);

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * Caffeine本地缓存
     */
    private Cache<String, I18nData> localCache;

    @PostConstruct
    public void init() {
        // 初始化Caffeine本地缓存
        this.localCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterWrite(30, TimeUnit.MINUTES)
                .build();

        log.info("I18n cache manager initialized with Caffeine local cache and Redis distributed cache");
    }

    /**
     * 生成缓存键
     *
     * @param applicationName 应用名称
     * @param languageTag 语言标签
     * @param code 国际化代码
     * @return 缓存键
     */
    private String generateCacheKey(String applicationName, String languageTag, String code) {
        return String.format("%s:%s:%s", applicationName, languageTag, code);
    }

    /**
     * 生成Redis键
     *
     * @param cacheKey 缓存键
     * @return Redis键
     */
    private String generateRedisKey(String cacheKey) {
        return REDIS_KEY_PREFIX + cacheKey;
    }

    /**
     * 获取国际化数据
     * 优先从本地缓存获取，如果没有则从Redis获取，都没有返回null
     *
     * @param applicationName 应用名称
     * @param languageTag 语言标签
     * @param code 国际化代码
     * @return 国际化数据
     */
    public I18nData get(String applicationName, String languageTag, String code) {
        String cacheKey = generateCacheKey(applicationName, languageTag, code);

        // 1. 先从本地缓存获取
        I18nData data = localCache.getIfPresent(cacheKey);
        if (data != null) {
            log.debug("Hit local cache for key: {}", cacheKey);
            return data;
        }

        // 2. 从Redis获取
        try {
            String redisKey = generateRedisKey(cacheKey);
            data = (I18nData) redisTemplate.opsForValue().get(redisKey);
            if (data != null) {
                log.debug("Hit Redis cache for key: {}", cacheKey);
                // 放入本地缓存
                localCache.put(cacheKey, data);
                return data;
            }
        } catch (Exception e) {
            log.warn("Failed to get data from Redis cache for key: {}, error: {}", cacheKey, e.getMessage());
        }

        log.debug("Cache miss for key: {}", cacheKey);
        return null;
    }

    /**
     * 缓存国际化数据
     * 同时放入本地缓存和Redis缓存
     *
     * @param data 国际化数据
     */
    public void put(I18nData data) {
        if (data == null || data.getApplicationName() == null ||
                data.getLanguageTag() == null || data.getCode() == null) {
            return;
        }

        String cacheKey = generateCacheKey(data.getApplicationName(), data.getLanguageTag(), data.getCode());

        // 1. 放入本地缓存
        localCache.put(cacheKey, data);

        // 2. 放入Redis缓存
        try {
            String redisKey = generateRedisKey(cacheKey);
            redisTemplate.opsForValue().set(redisKey, data, REDIS_EXPIRE_TIME);
            log.debug("Cached data for key: {}", cacheKey);
        } catch (Exception e) {
            log.warn("Failed to put data to Redis cache for key: {}, error: {}", cacheKey, e.getMessage());
        }
    }

    /**
     * 删除缓存
     *
     * @param applicationName 应用名称
     * @param languageTag 语言标签
     * @param code 国际化代码
     */
    public void evict(String applicationName, String languageTag, String code) {
        String cacheKey = generateCacheKey(applicationName, languageTag, code);

        // 1. 删除本地缓存
        localCache.invalidate(cacheKey);

        // 2. 删除Redis缓存
        try {
            String redisKey = generateRedisKey(cacheKey);
            redisTemplate.delete(redisKey);
            log.debug("Evicted cache for key: {}", cacheKey);
        } catch (Exception e) {
            log.warn("Failed to evict Redis cache for key: {}, error: {}", cacheKey, e.getMessage());
        }
    }

    /**
     * 清空指定应用的所有缓存
     *
     * @param applicationName 应用名称
     */
    public void evictByApplication(String applicationName) {
        // 1. 清空本地缓存中匹配的键
        localCache.asMap().keySet().removeIf(key -> key.startsWith(applicationName + ":"));

        // 2. 清空Redis缓存中匹配的键
        try {
            String pattern = REDIS_KEY_PREFIX + applicationName + ":*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.debug("Evicted all cache for application: {}", applicationName);
        } catch (Exception e) {
            log.warn("Failed to evict Redis cache for application: {}, error: {}", applicationName, e.getMessage());
        }
    }

    /**
     * 清空所有缓存
     */
    public void evictAll() {
        // 1. 清空本地缓存
        localCache.invalidateAll();

        // 2. 清空Redis缓存
        try {
            String pattern = REDIS_KEY_PREFIX + "*";
            redisTemplate.delete(redisTemplate.keys(pattern));
            log.debug("Evicted all i18n cache");
        } catch (Exception e) {
            log.warn("Failed to evict all Redis cache, error: {}", e.getMessage());
        }
    }

    /**
     * 获取本地缓存统计信息
     *
     * @return 缓存统计信息
     */
    public String getCacheStats() {
        return String.format("Local cache stats - size: %d, hit rate: %.2f%%",
                localCache.estimatedSize(),
                localCache.stats().hitRate() * 100);
    }
}
