package com.trinasolar.admin.i18n.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.trinasolar.admin.i18n.common.Result;
import com.trinasolar.admin.i18n.dto.I18nDataDTO;
import com.trinasolar.admin.i18n.dto.I18nDataQueryDTO;
import com.trinasolar.admin.i18n.entity.I18nData;
import com.trinasolar.admin.i18n.service.I18nDataService;
import com.trinasolar.admin.i18n.vo.I18nDataVO;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 国际化数据控制器
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@RestController
@RequestMapping("/api/i18n")
@Validated
@RequiredArgsConstructor
public class I18nDataController {

    private final I18nDataService i18nDataService;

    /**
     * 分页查询国际化数据
     */
    @GetMapping("/page")
    public Result<IPage<I18nDataVO>> page(I18nDataQueryDTO queryDTO) {
        try {
            IPage<I18nDataVO> result = i18nDataService.pageQuery(queryDTO);
            return Result.success(result);
        } catch (Exception e) {
            log.error("Failed to query i18n data page", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 根据ID查询国际化数据
     */
    @GetMapping("/{id}")
    public Result<I18nDataVO> getById(@PathVariable Long id) {
        try {
            I18nDataVO result = i18nDataService.getById(id);
            if (result == null) {
                return Result.error("数据不存在");
            }
            return Result.success(result);
        } catch (Exception e) {
            log.error("Failed to get i18n data by id: {}", id, e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 新增国际化数据
     */
    @PostMapping
    public Result<String> create(@Valid @RequestBody I18nDataDTO dto) {
        try {
            boolean success = i18nDataService.create(dto);
            return success ? Result.success("创建成功") : Result.error("创建失败");
        } catch (Exception e) {
            log.error("Failed to create i18n data", e);
            return Result.error("创建失败：" + e.getMessage());
        }
    }

    /**
     * 更新国际化数据
     */
    @PutMapping("/{id}")
    public Result<String> update(@PathVariable Long id, @Valid @RequestBody I18nDataDTO dto) {
        try {
            boolean success = i18nDataService.update(id, dto);
            return success ? Result.success("更新成功") : Result.error("更新失败");
        } catch (Exception e) {
            log.error("Failed to update i18n data: {}", id, e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }

    /**
     * 删除国际化数据
     */
    @DeleteMapping("/{id}")
    public Result<String> delete(@PathVariable Long id) {
        try {
            boolean success = i18nDataService.delete(id);
            return success ? Result.success("删除成功") : Result.error("删除失败");
        } catch (Exception e) {
            log.error("Failed to delete i18n data: {}", id, e);
            return Result.error("删除失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除国际化数据
     */
    @DeleteMapping("/batch")
    public Result<String> batchDelete(@RequestBody List<Long> ids) {
        try {
            boolean success = i18nDataService.batchDelete(ids);
            return success ? Result.success("批量删除成功") : Result.error("批量删除失败");
        } catch (Exception e) {
            log.error("Failed to batch delete i18n data", e);
            return Result.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 获取国际化消息
     */
    @GetMapping("/message")
    public Result<String> getMessage(@RequestParam String applicationName,
                                     @RequestParam String languageTag,
                                     @RequestParam String code) {
        try {
            String message = i18nDataService.getMessage(applicationName, languageTag, code);
            return Result.success(message);
        } catch (Exception e) {
            log.error("Failed to get i18n message", e);
            return Result.error("获取消息失败：" + e.getMessage());
        }
    }

    /**
     * 根据应用名称和语言标签获取国际化数据
     */
    @GetMapping("/list")
    public Result<List<I18nData>> getByApplicationAndLanguage(@RequestParam String applicationName,
                                                              @RequestParam String languageTag) {
        try {
            List<I18nData> result = i18nDataService.getByApplicationAndLanguage(applicationName, languageTag);
            return Result.success(result);
        } catch (Exception e) {
            log.error("Failed to get i18n data by application and language", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有应用名称
     */
    @GetMapping("/applications")
    public Result<List<String>> getAllApplicationNames() {
        try {
            List<String> result = i18nDataService.getAllApplicationNames();
            return Result.success(result);
        } catch (Exception e) {
            log.error("Failed to get all application names", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有语言标签
     */
    @GetMapping("/languages")
    public Result<List<String>> getAllLanguageTags() {
        try {
            List<String> result = i18nDataService.getAllLanguageTags();
            return Result.success(result);
        } catch (Exception e) {
            log.error("Failed to get all language tags", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 导出国际化数据到Excel
     */
    @PostMapping("/export")
    public void exportToExcel(@RequestBody I18nDataQueryDTO queryDTO, HttpServletResponse response) {
        try {
            i18nDataService.exportToExcel(queryDTO, response);
        } catch (Exception e) {
            log.error("Failed to export i18n data to Excel", e);
            throw new RuntimeException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 从Excel导入国际化数据
     */
    @PostMapping("/import")
    public Result<String> importFromExcel(@RequestParam("file") MultipartFile file) {
        try {
            if (file.isEmpty()) {
                return Result.error("请选择要导入的文件");
            }

            String result = i18nDataService.importFromExcel(file);
            return Result.success("导入完成", result);
        } catch (Exception e) {
            log.error("Failed to import i18n data from Excel", e);
            return Result.error("导入失败：" + e.getMessage());
        }
    }

    /**
     * 刷新缓存
     */
    @PostMapping("/cache/refresh")
    public Result<String> refreshCache(@RequestParam(required = false) String applicationName) {
        try {
            i18nDataService.refreshCache(applicationName);
            return Result.success("缓存刷新成功");
        } catch (Exception e) {
            log.error("Failed to refresh cache", e);
            return Result.error("缓存刷新失败：" + e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     */
    @GetMapping("/cache/stats")
    public Result<String> getCacheStats() {
        try {
            String stats = i18nDataService.getCacheStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("Failed to get cache stats", e);
            return Result.error("获取缓存统计失败：" + e.getMessage());
        }
    }
}
