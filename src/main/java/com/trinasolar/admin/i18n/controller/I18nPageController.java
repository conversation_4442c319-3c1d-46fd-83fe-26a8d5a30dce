package com.trinasolar.admin.i18n.controller;

import com.trinasolar.admin.i18n.service.I18nDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 国际化管理页面控制器
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Controller
@RequestMapping("/i18n")
@RequiredArgsConstructor
public class I18nPageController {

    private final I18nDataService i18nDataService;

    /**
     * 国际化管理首页
     */
    @GetMapping
    public String index(Model model) {
        try {
            // 获取所有应用名称和语言标签，用于下拉框
            List<String> applicationNames = i18nDataService.getAllApplicationNames();
            List<String> languageTags = i18nDataService.getAllLanguageTags();

            model.addAttribute("applicationNames", applicationNames);
            model.addAttribute("languageTags", languageTags);

            return "i18n/index";
        } catch (Exception e) {
            model.addAttribute("error", "加载页面失败：" + e.getMessage());
            return "error";
        }
    }

    /**
     * 缓存管理页面
     */
    @GetMapping("/cache")
    public String cache(Model model) {
        try {
            String cacheStats = i18nDataService.getCacheStats();
            model.addAttribute("cacheStats", cacheStats);
            return "i18n/cache";
        } catch (Exception e) {
            model.addAttribute("error", "加载缓存页面失败：" + e.getMessage());
            return "error";
        }
    }
}
