package com.trinasolar.admin.i18n.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 国际化数据传输对象
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class I18nDataDTO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用名称
     */
    @NotBlank(message = "应用名称不能为空")
    @Size(max = 50, message = "应用名称长度不能超过50个字符")
    private String applicationName;

    /**
     * 语言标签，如：zh-CN, en-US
     */
    @NotBlank(message = "语言标签不能为空")
    @Size(max = 10, message = "语言标签长度不能超过10个字符")
    private String languageTag;

    /**
     * 国际化唯一标识，格式：业务模块.功能.字段
     */
    @NotBlank(message = "国际化标识不能为空")
    @Size(max = 200, message = "国际化标识长度不能超过200个字符")
    private String code;

    /**
     * 国际化文本内容，支持占位符 {0}, {1}
     */
    private String message;

    /**
     * 备注说明
     */
    @Size(max = 500, message = "备注长度不能超过500个字符")
    private String remark;
}
