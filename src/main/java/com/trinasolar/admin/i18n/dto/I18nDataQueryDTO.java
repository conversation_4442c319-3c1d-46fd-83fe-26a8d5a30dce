package com.trinasolar.admin.i18n.dto;

import lombok.Data;

/**
 * 国际化数据查询对象
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class I18nDataQueryDTO {

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 语言标签，如：zh-CN, en-US
     */
    private String languageTag;

    /**
     * 国际化唯一标识，格式：业务模块.功能.字段
     */
    private String code;

    /**
     * 国际化文本内容，支持模糊查询
     */
    private String message;

    /**
     * 当前页码
     */
    private Long current = 1L;

    /**
     * 每页大小
     */
    private Long size = 10L;
}
