package com.trinasolar.admin.i18n.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 国际化数据实体类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("i18n_data")
public class I18nData {

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 应用名称
     */
    @TableField("application_name")
    private String applicationName;

    /**
     * 语言标签，如：zh-CN, en-US
     */
    @TableField("language_tag")
    private String languageTag;

    /**
     * 国际化唯一标识，格式：业务模块.功能.字段
     */
    @TableField("code")
    private String code;

    /**
     * 国际化文本内容，支持占位符 {0}, {1}
     */
    @TableField("message")
    private String message;

    /**
     * 备注说明
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 逻辑删除标识，0-未删除，1-已删除
     */
    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
