package com.trinasolar.admin.i18n.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trinasolar.admin.i18n.dto.I18nDataQueryDTO;
import com.trinasolar.admin.i18n.entity.I18nData;
import com.trinasolar.admin.i18n.vo.I18nDataVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 国际化数据Mapper接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Mapper
public interface I18nDataMapper extends BaseMapper<I18nData> {

    /**
     * 分页查询国际化数据
     *
     * @param page 分页参数
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<I18nDataVO> selectI18nDataPage(Page<I18nDataVO> page, @Param("query") I18nDataQueryDTO queryDTO);

    /**
     * 根据应用名称和语言标签查询国际化数据
     *
     * @param applicationName 应用名称
     * @param languageTag 语言标签
     * @return 国际化数据列表
     */
    List<I18nData> selectByApplicationAndLanguage(@Param("applicationName") String applicationName, 
                                                  @Param("languageTag") String languageTag);

    /**
     * 根据应用名称、语言标签和代码查询国际化数据
     *
     * @param applicationName 应用名称
     * @param languageTag 语言标签
     * @param code 国际化代码
     * @return 国际化数据
     */
    I18nData selectByApplicationLanguageAndCode(@Param("applicationName") String applicationName,
                                                @Param("languageTag") String languageTag,
                                                @Param("code") String code);

    /**
     * 批量插入国际化数据
     *
     * @param dataList 数据列表
     * @return 插入数量
     */
    int batchInsert(@Param("dataList") List<I18nData> dataList);

    /**
     * 获取所有应用名称
     *
     * @return 应用名称列表
     */
    List<String> selectAllApplicationNames();

    /**
     * 获取所有语言标签
     *
     * @return 语言标签列表
     */
    List<String> selectAllLanguageTags();
}
