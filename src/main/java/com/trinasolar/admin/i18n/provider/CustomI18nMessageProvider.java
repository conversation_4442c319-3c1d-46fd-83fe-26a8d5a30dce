package com.trinasolar.admin.i18n.provider;

import com.trinasolar.admin.i18n.cache.I18nCacheManager;
import com.trinasolar.admin.i18n.entity.I18nData;
import com.trinasolar.admin.i18n.mapper.I18nDataMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.MessageSourceResolvable;
import org.springframework.context.NoSuchMessageException;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.text.MessageFormat;
import java.util.Locale;

/**
 * 自定义国际化消息提供者
 * 实现Spring的MessageSource接口，提供基于数据库的国际化消息
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
public class CustomI18nMessageProvider implements MessageSource {

    private static final String DEFAULT_APPLICATION_NAME = "trina-admin";

    @Autowired
    private I18nDataMapper i18nDataMapper;

    @Autowired
    private I18nCacheManager cacheManager;

    /**
     * 获取国际化消息
     *
     * @param code 消息代码
     * @param args 参数数组
     * @param defaultMessage 默认消息
     * @param locale 语言环境
     * @return 国际化消息
     */
    @Override
    public String getMessage(String code, Object[] args, String defaultMessage, Locale locale) {
        try {
            String message = getMessageInternal(code, locale);
            if (message != null) {
                return formatMessage(message, args);
            }
        } catch (Exception e) {
            log.warn("Failed to get i18n message for code: {}, locale: {}, error: {}", code, locale, e.getMessage());
        }
        
        return defaultMessage != null ? defaultMessage : code;
    }

    /**
     * 获取国际化消息（必须存在，否则抛出异常）
     *
     * @param code 消息代码
     * @param args 参数数组
     * @param locale 语言环境
     * @return 国际化消息
     * @throws NoSuchMessageException 如果消息不存在
     */
    @Override
    public String getMessage(String code, Object[] args, Locale locale) throws NoSuchMessageException {
        String message = getMessageInternal(code, locale);
        if (message == null) {
            throw new NoSuchMessageException(code, locale);
        }
        return formatMessage(message, args);
    }

    /**
     * 获取国际化消息
     *
     * @param resolvable 消息解析对象
     * @param locale 语言环境
     * @return 国际化消息
     * @throws NoSuchMessageException 如果消息不存在
     */
    @Override
    public String getMessage(MessageSourceResolvable resolvable, Locale locale) throws NoSuchMessageException {
        String[] codes = resolvable.getCodes();
        if (codes != null) {
            for (String code : codes) {
                String message = getMessageInternal(code, locale);
                if (message != null) {
                    return formatMessage(message, resolvable.getArguments());
                }
            }
        }
        
        String defaultMessage = resolvable.getDefaultMessage();
        if (defaultMessage != null) {
            return defaultMessage;
        }
        
        throw new NoSuchMessageException(codes != null && codes.length > 0 ? codes[0] : "unknown", locale);
    }

    /**
     * 根据应用名称获取国际化消息
     *
     * @param applicationName 应用名称
     * @param code 消息代码
     * @param args 参数数组
     * @param defaultMessage 默认消息
     * @param locale 语言环境
     * @return 国际化消息
     */
    public String getMessage(String applicationName, String code, Object[] args, String defaultMessage, Locale locale) {
        try {
            String message = getMessageInternal(applicationName, code, locale);
            if (message != null) {
                return formatMessage(message, args);
            }
        } catch (Exception e) {
            log.warn("Failed to get i18n message for app: {}, code: {}, locale: {}, error: {}", 
                    applicationName, code, locale, e.getMessage());
        }
        
        return defaultMessage != null ? defaultMessage : code;
    }

    /**
     * 内部获取消息方法（使用默认应用名称）
     *
     * @param code 消息代码
     * @param locale 语言环境
     * @return 国际化消息
     */
    private String getMessageInternal(String code, Locale locale) {
        return getMessageInternal(DEFAULT_APPLICATION_NAME, code, locale);
    }

    /**
     * 内部获取消息方法
     *
     * @param applicationName 应用名称
     * @param code 消息代码
     * @param locale 语言环境
     * @return 国际化消息
     */
    private String getMessageInternal(String applicationName, String code, Locale locale) {
        if (!StringUtils.hasText(code) || locale == null) {
            return null;
        }

        String languageTag = locale.toLanguageTag();
        
        // 1. 先从缓存获取
        I18nData cachedData = cacheManager.get(applicationName, languageTag, code);
        if (cachedData != null) {
            return cachedData.getMessage();
        }

        // 2. 从数据库查询
        I18nData data = i18nDataMapper.selectByApplicationLanguageAndCode(applicationName, languageTag, code);
        if (data != null) {
            // 放入缓存
            cacheManager.put(data);
            return data.getMessage();
        }

        // 3. 如果是特定语言没找到，尝试查找默认语言（去掉国家代码）
        if (languageTag.contains("-")) {
            String baseLanguage = languageTag.split("-")[0];
            I18nData baseData = i18nDataMapper.selectByApplicationLanguageAndCode(applicationName, baseLanguage, code);
            if (baseData != null) {
                // 放入缓存
                cacheManager.put(baseData);
                return baseData.getMessage();
            }
        }

        return null;
    }

    /**
     * 格式化消息（处理占位符）
     *
     * @param message 原始消息
     * @param args 参数数组
     * @return 格式化后的消息
     */
    private String formatMessage(String message, Object[] args) {
        if (message == null) {
            return null;
        }
        
        if (args == null || args.length == 0) {
            return message;
        }

        try {
            return MessageFormat.format(message, args);
        } catch (Exception e) {
            log.warn("Failed to format message: {}, args: {}, error: {}", message, args, e.getMessage());
            return message;
        }
    }
}
