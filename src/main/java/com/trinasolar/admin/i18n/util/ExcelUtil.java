package com.trinasolar.admin.i18n.util;

import com.trinasolar.admin.i18n.dto.I18nDataDTO;
import com.trinasolar.admin.i18n.vo.I18nDataVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel工具类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Component
public class ExcelUtil {

    private static final String[] EXPORT_HEADERS = {
            "应用名称", "语言标签", "国际化标识", "文本内容", "备注", "创建时间", "更新时间", "创建人", "更新人"
    };

    private static final String[] IMPORT_HEADERS = {
            "应用名称", "语言标签", "国际化标识", "文本内容", "备注"
    };

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 导出国际化数据到Excel
     *
     * @param dataList 数据列表
     * @param response HTTP响应
     */
    public void exportI18nData(List<I18nDataVO> dataList, HttpServletResponse response) {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("国际化数据");

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            CellStyle headerStyle = createHeaderStyle(workbook);

            for (int i = 0; i < EXPORT_HEADERS.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(EXPORT_HEADERS[i]);
                cell.setCellStyle(headerStyle);
                sheet.autoSizeColumn(i);
            }

            // 创建数据行
            CellStyle dataStyle = createDataStyle(workbook);
            for (int i = 0; i < dataList.size(); i++) {
                Row row = sheet.createRow(i + 1);
                I18nDataVO data = dataList.get(i);

                createCell(row, 0, data.getApplicationName(), dataStyle);
                createCell(row, 1, data.getLanguageTag(), dataStyle);
                createCell(row, 2, data.getCode(), dataStyle);
                createCell(row, 3, data.getMessage(), dataStyle);
                createCell(row, 4, data.getRemark(), dataStyle);
                createCell(row, 5, formatDateTime(data.getCreateTime()), dataStyle);
                createCell(row, 6, formatDateTime(data.getUpdateTime()), dataStyle);
                createCell(row, 7, data.getCreateBy(), dataStyle);
                createCell(row, 8, data.getUpdateBy(), dataStyle);
            }

            // 自动调整列宽
            for (int i = 0; i < EXPORT_HEADERS.length; i++) {
                sheet.autoSizeColumn(i);
            }

            // 设置响应头
            String fileName = "i18n_data_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));

            // 写入响应流
            try (OutputStream outputStream = response.getOutputStream()) {
                workbook.write(outputStream);
                outputStream.flush();
            }

            log.info("Exported {} i18n data records to Excel", dataList.size());

        } catch (IOException e) {
            log.error("Failed to export i18n data to Excel", e);
            throw new RuntimeException("导出Excel失败：" + e.getMessage());
        }
    }

    /**
     * 从Excel导入国际化数据
     *
     * @param file Excel文件
     * @return 数据列表
     */
    public List<I18nDataDTO> importI18nData(MultipartFile file) {
        List<I18nDataDTO> dataList = new ArrayList<>();

        try (InputStream inputStream = file.getInputStream();
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                throw new RuntimeException("Excel文件格式错误：找不到工作表");
            }

            // 验证标题行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                throw new RuntimeException("Excel文件格式错误：找不到标题行");
            }

            validateHeaders(headerRow);

            // 读取数据行
            int lastRowNum = sheet.getLastRowNum();
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }

                try {
                    I18nDataDTO dto = parseRowToDTO(row);
                    if (dto != null) {
                        dataList.add(dto);
                    }
                } catch (Exception e) {
                    log.warn("Failed to parse row {}: {}", i + 1, e.getMessage());
                    throw new RuntimeException("第" + (i + 1) + "行数据解析失败：" + e.getMessage());
                }
            }

            log.info("Imported {} i18n data records from Excel", dataList.size());
            return dataList;

        } catch (IOException e) {
            log.error("Failed to import i18n data from Excel", e);
            throw new RuntimeException("导入Excel失败：" + e.getMessage());
        }
    }

    /**
     * 创建标题样式
     */
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);

        return style;
    }

    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setWrapText(true);
        return style;
    }

    /**
     * 创建单元格
     */
    private void createCell(Row row, int columnIndex, String value, CellStyle style) {
        Cell cell = row.createCell(columnIndex);
        cell.setCellValue(value != null ? value : "");
        cell.setCellStyle(style);
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATE_FORMATTER) : "";
    }

    /**
     * 验证标题行
     */
    private void validateHeaders(Row headerRow) {
        for (int i = 0; i < IMPORT_HEADERS.length; i++) {
            Cell cell = headerRow.getCell(i);
            String headerValue = getCellStringValue(cell);
            if (!IMPORT_HEADERS[i].equals(headerValue)) {
                throw new RuntimeException("Excel文件格式错误：第" + (i + 1) + "列标题应为'" + IMPORT_HEADERS[i] + "'，实际为'" + headerValue + "'");
            }
        }
    }

    /**
     * 判断是否为空行
     */
    private boolean isEmptyRow(Row row) {
        for (int i = 0; i < IMPORT_HEADERS.length; i++) {
            Cell cell = row.getCell(i);
            if (StringUtils.hasText(getCellStringValue(cell))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 解析行数据为DTO
     */
    private I18nDataDTO parseRowToDTO(Row row) {
        I18nDataDTO dto = new I18nDataDTO();

        dto.setApplicationName(getCellStringValue(row.getCell(0)));
        dto.setLanguageTag(getCellStringValue(row.getCell(1)));
        dto.setCode(getCellStringValue(row.getCell(2)));
        dto.setMessage(getCellStringValue(row.getCell(3)));
        dto.setRemark(getCellStringValue(row.getCell(4)));

        // 验证必填字段
        if (!StringUtils.hasText(dto.getApplicationName()) ||
                !StringUtils.hasText(dto.getLanguageTag()) ||
                !StringUtils.hasText(dto.getCode())) {
            throw new RuntimeException("应用名称、语言标签、国际化标识不能为空");
        }

        return dto;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
}
