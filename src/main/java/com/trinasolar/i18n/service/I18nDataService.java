package com.trinasolar.i18n.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.trinasolar.i18n.dto.I18nDataDTO;
import com.trinasolar.i18n.dto.I18nDataQueryDTO;
import com.trinasolar.i18n.entity.I18nData;
import com.trinasolar.i18n.vo.I18nDataVO;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 国际化数据服务接口
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface I18nDataService extends IService<I18nData> {

    /**
     * 分页查询国际化数据
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    IPage<I18nDataVO> pageQuery(I18nDataQueryDTO queryDTO);

    /**
     * 根据ID查询国际化数据
     *
     * @param id 主键ID
     * @return 国际化数据VO
     */
    I18nDataVO getById(Long id);

    /**
     * 新增国际化数据
     *
     * @param dto 国际化数据DTO
     * @return 是否成功
     */
    boolean create(I18nDataDTO dto);

    /**
     * 更新国际化数据
     *
     * @param id 主键ID
     * @param dto 国际化数据DTO
     * @return 是否成功
     */
    boolean update(Long id, I18nDataDTO dto);

    /**
     * 删除国际化数据
     *
     * @param id 主键ID
     * @return 是否成功
     */
    boolean delete(Long id);

    /**
     * 批量删除国际化数据
     *
     * @param ids 主键ID列表
     * @return 是否成功
     */
    boolean batchDelete(List<Long> ids);

    /**
     * 根据应用名称和语言标签获取国际化数据
     *
     * @param applicationName 应用名称
     * @param languageTag 语言标签
     * @return 国际化数据列表
     */
    List<I18nData> getByApplicationAndLanguage(String applicationName, String languageTag);

    /**
     * 获取国际化消息
     *
     * @param applicationName 应用名称
     * @param languageTag 语言标签
     * @param code 国际化代码
     * @return 国际化消息
     */
    String getMessage(String applicationName, String languageTag, String code);

    /**
     * 获取所有应用名称
     *
     * @return 应用名称列表
     */
    List<String> getAllApplicationNames();

    /**
     * 获取所有语言标签
     *
     * @return 语言标签列表
     */
    List<String> getAllLanguageTags();

    /**
     * 导出国际化数据到Excel
     *
     * @param queryDTO 查询条件
     * @param response HTTP响应
     */
    void exportToExcel(I18nDataQueryDTO queryDTO, HttpServletResponse response);

    /**
     * 从Excel导入国际化数据
     *
     * @param file Excel文件
     * @return 导入结果信息
     */
    String importFromExcel(MultipartFile file);

    /**
     * 刷新缓存
     *
     * @param applicationName 应用名称，为空则刷新所有
     */
    void refreshCache(String applicationName);

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    String getCacheStats();
}
