package com.trinasolar.i18n.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.trinasolar.i18n.cache.I18nCacheManager;
import com.trinasolar.i18n.dto.I18nDataDTO;
import com.trinasolar.i18n.dto.I18nDataQueryDTO;
import com.trinasolar.i18n.entity.I18nData;
import com.trinasolar.i18n.mapper.I18nDataMapper;
import com.trinasolar.i18n.service.I18nDataService;
import com.trinasolar.i18n.util.ExcelUtil;
import com.trinasolar.i18n.vo.I18nDataVO;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 国际化数据服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class I18nDataServiceImpl extends ServiceImpl<I18nDataMapper, I18nData> implements I18nDataService {

    private final I18nDataMapper i18nDataMapper;

    private final I18nCacheManager cacheManager;

    private final ExcelUtil excelUtil;

    @Override
    public IPage<I18nDataVO> pageQuery(I18nDataQueryDTO queryDTO) {
        Page<I18nDataVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        return i18nDataMapper.selectI18nDataPage(page, queryDTO);
    }

    @Override
    public I18nDataVO getById(Long id) {
        I18nData entity = super.getById(id);
        if (entity == null) {
            return null;
        }

        I18nDataVO vo = new I18nDataVO();
        BeanUtils.copyProperties(entity, vo);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(I18nDataDTO dto) {
        // 检查是否已存在相同的记录
        I18nData existing = i18nDataMapper.selectByApplicationLanguageAndCode(
                dto.getApplicationName(), dto.getLanguageTag(), dto.getCode());
        if (existing != null) {
            throw new RuntimeException("国际化配置已存在：" + dto.getCode());
        }

        I18nData entity = new I18nData();
        BeanUtils.copyProperties(dto, entity);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreateBy("system"); // TODO: 从当前用户获取
        entity.setUpdateBy("system");

        boolean result = super.save(entity);
        if (result) {
            // 放入缓存
            cacheManager.put(entity);
            log.info("Created i18n data: {}", entity.getCode());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(Long id, I18nDataDTO dto) {
        I18nData existing = super.getById(id);
        if (existing == null) {
            throw new RuntimeException("国际化配置不存在");
        }

        // 如果修改了关键字段，需要检查是否与其他记录冲突
        if (!existing.getApplicationName().equals(dto.getApplicationName()) ||
                !existing.getLanguageTag().equals(dto.getLanguageTag()) ||
                !existing.getCode().equals(dto.getCode())) {

            I18nData conflict = i18nDataMapper.selectByApplicationLanguageAndCode(
                    dto.getApplicationName(), dto.getLanguageTag(), dto.getCode());
            if (conflict != null && !conflict.getId().equals(id)) {
                throw new RuntimeException("国际化配置已存在：" + dto.getCode());
            }
        }

        // 先删除旧缓存
        cacheManager.evict(existing.getApplicationName(), existing.getLanguageTag(), existing.getCode());

        I18nData entity = new I18nData();
        BeanUtils.copyProperties(dto, entity);
        entity.setId(id);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy("system"); // TODO: 从当前用户获取

        boolean result = super.updateById(entity);
        if (result) {
            // 放入新缓存
            cacheManager.put(entity);
            log.info("Updated i18n data: {}", entity.getCode());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Long id) {
        I18nData existing = super.getById(id);
        if (existing == null) {
            return false;
        }

        boolean result = super.removeById(id);
        if (result) {
            // 删除缓存
            cacheManager.evict(existing.getApplicationName(), existing.getLanguageTag(), existing.getCode());
            log.info("Deleted i18n data: {}", existing.getCode());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDelete(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        // 先查询要删除的记录，用于清除缓存
        List<I18nData> toDelete = super.listByIds(ids);

        boolean result = super.removeByIds(ids);
        if (result) {
            // 批量删除缓存
            toDelete.forEach(data ->
                    cacheManager.evict(data.getApplicationName(), data.getLanguageTag(), data.getCode()));
            log.info("Batch deleted {} i18n data records", ids.size());
        }
        return result;
    }

    @Override
    public List<I18nData> getByApplicationAndLanguage(String applicationName, String languageTag) {
        return i18nDataMapper.selectByApplicationAndLanguage(applicationName, languageTag);
    }

    @Override
    public String getMessage(String applicationName, String languageTag, String code) {
        // 先从缓存获取
        I18nData cachedData = cacheManager.get(applicationName, languageTag, code);
        if (cachedData != null) {
            return cachedData.getMessage();
        }

        // 从数据库查询
        I18nData data = i18nDataMapper.selectByApplicationLanguageAndCode(applicationName, languageTag, code);
        if (data != null) {
            cacheManager.put(data);
            return data.getMessage();
        }

        return null;
    }

    @Override
    public List<String> getAllApplicationNames() {
        return i18nDataMapper.selectAllApplicationNames();
    }

    @Override
    public List<String> getAllLanguageTags() {
        return i18nDataMapper.selectAllLanguageTags();
    }

    @Override
    public void exportToExcel(I18nDataQueryDTO queryDTO, HttpServletResponse response) {
        // 查询所有符合条件的数据
        queryDTO.setCurrent(1L);
        queryDTO.setSize(Long.MAX_VALUE);
        IPage<I18nDataVO> pageResult = pageQuery(queryDTO);

        List<I18nDataVO> dataList = pageResult.getRecords();
        excelUtil.exportI18nData(dataList, response);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importFromExcel(MultipartFile file) {
        try {
            List<I18nDataDTO> dataList = excelUtil.importI18nData(file);
            if (dataList.isEmpty()) {
                return "导入文件为空";
            }

            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMsg = new StringBuilder();

            for (I18nDataDTO dto : dataList) {
                try {
                    // 检查必填字段
                    if (!StringUtils.hasText(dto.getApplicationName()) ||
                            !StringUtils.hasText(dto.getLanguageTag()) ||
                            !StringUtils.hasText(dto.getCode())) {
                        failCount++;
                        errorMsg.append("第").append(successCount + failCount).append("行：必填字段不能为空\n");
                        continue;
                    }

                    // 检查是否已存在
                    I18nData existing = i18nDataMapper.selectByApplicationLanguageAndCode(
                            dto.getApplicationName(), dto.getLanguageTag(), dto.getCode());

                    if (existing != null) {
                        // 更新现有记录
                        update(existing.getId(), dto);
                    } else {
                        // 创建新记录
                        create(dto);
                    }
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    errorMsg.append("第").append(successCount + failCount).append("行：").append(e.getMessage()).append("\n");
                }
            }

            String result = String.format("导入完成：成功 %d 条，失败 %d 条", successCount, failCount);
            if (errorMsg.length() > 0) {
                result += "\n错误详情：\n" + errorMsg.toString();
            }

            log.info("Import i18n data completed: success={}, fail={}", successCount, failCount);
            return result;

        } catch (Exception e) {
            log.error("Import i18n data failed", e);
            throw new RuntimeException("导入失败：" + e.getMessage());
        }
    }

    @Override
    public void refreshCache(String applicationName) {
        if (StringUtils.hasText(applicationName)) {
            cacheManager.evictByApplication(applicationName);
            log.info("Refreshed cache for application: {}", applicationName);
        } else {
            cacheManager.evictAll();
            log.info("Refreshed all i18n cache");
        }
    }

    @Override
    public String getCacheStats() {
        return cacheManager.getCacheStats();
    }
}
