package com.trinasolar.i18n.vo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 国际化数据视图对象
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
public class I18nDataVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 应用名称
     */
    private String applicationName;

    /**
     * 语言标签，如：zh-CN, en-US
     */
    private String languageTag;

    /**
     * 国际化唯一标识，格式：业务模块.功能.字段
     */
    private String code;

    /**
     * 国际化文本内容，支持占位符 {0}, {1}
     */
    private String message;

    /**
     * 备注说明
     */
    private String remark;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;
}
