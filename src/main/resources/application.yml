server:
  port: 9091
spring:
  application:
    name: trina-i18n-console
  cache:
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=30m
    type: caffeine
  data:
    redis:
      database: 0
      host: localhost
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
      password: luocheng
      port: 6379
      timeout: 5000ms
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: luocheng
    url: ***********************************************************************************************************************
    username: root
  messages:
    basename: i18n/messages
    encoding: UTF-8
    fallback-to-system-locale: false
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.trinasolar.i18n.entity