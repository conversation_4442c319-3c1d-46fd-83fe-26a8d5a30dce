-- 创建数据库
CREATE DATABASE IF NOT EXISTS `trina_i18n` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `trina_i18n`;

-- 国际化数据表
CREATE TABLE `i18n_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `application_name` varchar(50) NOT NULL DEFAULT '' COMMENT '应用名称',
  `language_tag` varchar(10) NOT NULL COMMENT '语言标签，如：zh-CN, en-US',
  `code` varchar(200) NOT NULL COMMENT '国际化唯一标识，格式：业务模块.功能.字段',
  `message` text COMMENT '国际化文本内容，支持占位符 {0}, {1}',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注说明',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '逻辑删除标识，0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_app_lang_code` (`application_name`, `language_tag`, `code`, `deleted`),
  KEY `idx_application_name` (`application_name`),
  KEY `idx_language_tag` (`language_tag`),
  KEY `idx_code` (`code`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国际化数据表';

-- 插入示例数据
INSERT INTO `i18n_data` (`application_name`, `language_tag`, `code`, `message`, `remark`, `create_by`) VALUES
('trina-admin', 'zh-CN', 'menu.account', '个人中心', '菜单-个人中心', 'system'),
('trina-admin', 'en-US', 'menu.account', 'Account', 'Menu-Account', 'system'),
('trina-admin', 'zh-CN', 'menu.account.settings', '个人设置', '菜单-个人设置', 'system'),
('trina-admin', 'en-US', 'menu.account.settings', 'Account Settings', 'Menu-Account Settings', 'system'),
('trina-admin', 'zh-CN', 'menu.account.settings.base', '基本设置', '菜单-基本设置', 'system'),
('trina-admin', 'en-US', 'menu.account.settings.base', 'Base', 'Menu-Base Settings', 'system'),
('trina-admin', 'zh-CN', 'menu.account.settings.security', '安全设置', '菜单-安全设置', 'system'),
('trina-admin', 'en-US', 'menu.account.settings.security', 'Security', 'Menu-Security Settings', 'system'),
('trina-admin', 'zh-CN', 'menu.account.settings.binding', '账户绑定', '菜单-账户绑定', 'system'),
('trina-admin', 'en-US', 'menu.account.settings.binding', 'Binding', 'Menu-Account Binding', 'system'),
('trina-admin', 'zh-CN', 'common.operation', '操作', '通用-操作', 'system'),
('trina-admin', 'en-US', 'common.operation', 'Operation', 'Common-Operation', 'system'),
('trina-admin', 'zh-CN', 'common.edit', '编辑', '通用-编辑', 'system'),
('trina-admin', 'en-US', 'common.edit', 'Edit', 'Common-Edit', 'system'),
('trina-admin', 'zh-CN', 'common.delete', '删除', '通用-删除', 'system'),
('trina-admin', 'en-US', 'common.delete', 'Delete', 'Common-Delete', 'system'),
('trina-admin', 'zh-CN', 'common.add', '新增', '通用-新增', 'system'),
('trina-admin', 'en-US', 'common.add', 'Add', 'Common-Add', 'system'),
('trina-admin', 'zh-CN', 'common.query', '查询', '通用-查询', 'system'),
('trina-admin', 'en-US', 'common.query', 'Query', 'Common-Query', 'system'),
('trina-admin', 'zh-CN', 'common.export', '导出', '通用-导出', 'system'),
('trina-admin', 'en-US', 'common.export', 'Export', 'Common-Export', 'system'),
('trina-admin', 'zh-CN', 'common.import', '导入', '通用-导入', 'system'),
('trina-admin', 'en-US', 'common.import', 'Import', 'Common-Import', 'system');
