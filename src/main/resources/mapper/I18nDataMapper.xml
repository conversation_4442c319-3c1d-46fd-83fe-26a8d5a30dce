<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trinasolar.i18n.mapper.I18nDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.trinasolar.i18n.entity.I18nData">
        <id column="id" property="id" />
        <result column="application_name" property="applicationName" />
        <result column="language_tag" property="languageTag" />
        <result column="code" property="code" />
        <result column="message" property="message" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- VO查询映射结果 -->
    <resultMap id="VOResultMap" type="com.trinasolar.i18n.vo.I18nDataVO">
        <id column="id" property="id" />
        <result column="application_name" property="applicationName" />
        <result column="language_tag" property="languageTag" />
        <result column="code" property="code" />
        <result column="message" property="message" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, application_name, language_tag, code, message, remark, create_time, update_time, create_by, update_by, deleted
    </sql>

    <!-- 分页查询国际化数据 -->
    <select id="selectI18nDataPage" resultMap="VOResultMap">
        SELECT id, application_name, language_tag, code, message, remark, create_time, update_time, create_by, update_by
        FROM i18n_data
        WHERE deleted = 0
        <if test="query.applicationName != null and query.applicationName != ''">
            AND application_name LIKE CONCAT('%', #{query.applicationName}, '%')
        </if>
        <if test="query.languageTag != null and query.languageTag != ''">
            AND language_tag = #{query.languageTag}
        </if>
        <if test="query.code != null and query.code != ''">
            AND code LIKE CONCAT('%', #{query.code}, '%')
        </if>
        <if test="query.message != null and query.message != ''">
            AND message LIKE CONCAT('%', #{query.message}, '%')
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据应用名称和语言标签查询国际化数据 -->
    <select id="selectByApplicationAndLanguage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM i18n_data
        WHERE deleted = 0
        AND application_name = #{applicationName}
        AND language_tag = #{languageTag}
        ORDER BY code
    </select>

    <!-- 根据应用名称、语言标签和代码查询国际化数据 -->
    <select id="selectByApplicationLanguageAndCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM i18n_data
        WHERE deleted = 0
        AND application_name = #{applicationName}
        AND language_tag = #{languageTag}
        AND code = #{code}
        LIMIT 1
    </select>

    <!-- 批量插入国际化数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO i18n_data (application_name, language_tag, code, message, remark, create_time, update_time, create_by, update_by)
        VALUES
        <foreach collection="dataList" item="item" separator=",">
            (#{item.applicationName}, #{item.languageTag}, #{item.code}, #{item.message}, #{item.remark}, 
             #{item.createTime}, #{item.updateTime}, #{item.createBy}, #{item.updateBy})
        </foreach>
    </insert>

    <!-- 获取所有应用名称 -->
    <select id="selectAllApplicationNames" resultType="java.lang.String">
        SELECT DISTINCT application_name
        FROM i18n_data
        WHERE deleted = 0
        ORDER BY application_name
    </select>

    <!-- 获取所有语言标签 -->
    <select id="selectAllLanguageTags" resultType="java.lang.String">
        SELECT DISTINCT language_tag
        FROM i18n_data
        WHERE deleted = 0
        ORDER BY language_tag
    </select>

</mapper>
