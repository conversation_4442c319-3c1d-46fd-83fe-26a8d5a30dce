/* 国际化管理系统样式 - 现代简洁风格 */

/* 全局样式 */
body {
    background-color: #f5f7fa;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    font-size: 14px;
    color: #303133;
    line-height: 1.5;
}

/* 容器样式 */
.container-fluid {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    background-color: #fff;
    margin-bottom: 20px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    border-radius: 4px 4px 0 0 !important;
    padding: 18px 20px;
}

.card-body {
    padding: 20px;
}

/* 查询区域样式 */
.query-section {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.query-form .row {
    margin-bottom: 18px;
}

.query-form .row:last-child {
    margin-bottom: 0;
}

/* 表格样式 */
.table-container {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
    font-size: 14px;
}

.table th {
    border-top: none;
    border-bottom: 1px solid #ebeef5;
    font-weight: 500;
    background-color: #fafafa;
    color: #909399;
    vertical-align: middle;
    padding: 12px 15px;
    font-size: 13px;
}

.table td {
    vertical-align: middle;
    padding: 12px 15px;
    border-bottom: 1px solid #ebeef5;
    color: #606266;
}

.table tbody tr:hover {
    background-color: #f5f7fa;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    padding: 8px 15px;
    border: 1px solid transparent;
    transition: all 0.3s;
}

.btn-primary {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
}

.btn-primary:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
}

.btn-success {
    background-color: #67c23a;
    border-color: #67c23a;
    color: #fff;
}

.btn-success:hover {
    background-color: #85ce61;
    border-color: #85ce61;
}

.btn-danger {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
}

.btn-danger:hover {
    background-color: #f78989;
    border-color: #f78989;
}

.btn-warning {
    background-color: #e6a23c;
    border-color: #e6a23c;
    color: #fff;
}

.btn-warning:hover {
    background-color: #ebb563;
    border-color: #ebb563;
}

.btn-info {
    background-color: #909399;
    border-color: #909399;
    color: #fff;
}

.btn-info:hover {
    background-color: #a6a9ad;
    border-color: #a6a9ad;
}

.btn-outline-primary {
    color: #409eff;
    border-color: #409eff;
    background-color: transparent;
}

.btn-outline-primary:hover {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
}

.btn-outline-danger {
    color: #f56c6c;
    border-color: #f56c6c;
    background-color: transparent;
}

.btn-outline-danger:hover {
    background-color: #f56c6c;
    border-color: #f56c6c;
    color: #fff;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 3px;
}

/* 操作按钮组 */
.action-buttons {
    margin-bottom: 20px;
}

.action-buttons .btn {
    margin-right: 10px;
    margin-bottom: 10px;
}

/* 表单样式 */
.form-control, .form-select {
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    padding: 8px 12px;
    font-size: 14px;
    color: #606266;
    background-color: #fff;
    transition: border-color 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
    outline: none;
}

.form-control::placeholder {
    color: #c0c4cc;
}

.form-label {
    font-weight: 500;
    color: #606266;
    margin-bottom: 8px;
    font-size: 14px;
}

/* 输入框组样式 */
.input-group {
    margin-bottom: 18px;
}

.input-group .form-control {
    margin-bottom: 0;
}

/* 模态框样式 */
.modal-content {
    border-radius: 8px;
    border: none;
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid #ebeef5;
    border-radius: 8px 8px 0 0;
    padding: 20px 24px;
    background-color: #fafafa;
}

.modal-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    border-top: 1px solid #ebeef5;
    border-radius: 0 0 8px 8px;
    padding: 16px 24px;
    background-color: #fafafa;
}

.modal-footer .btn {
    margin-left: 8px;
}

.modal-footer .btn:first-child {
    margin-left: 0;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    border-top: 1px solid #ebeef5;
}

.pagination {
    margin-bottom: 0;
    display: flex;
    align-items: center;
}

.page-item {
    margin: 0 2px;
}

.page-link {
    color: #606266;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 13px;
    min-width: 32px;
    text-align: center;
    background-color: #fff;
    transition: all 0.3s;
}

.page-link:hover {
    color: #409eff;
    background-color: #ecf5ff;
    border-color: #b3d8ff;
}

.page-item.active .page-link {
    background-color: #409eff;
    border-color: #409eff;
    color: #fff;
}

.page-item.disabled .page-link {
    color: #c0c4cc;
    background-color: #fff;
    border-color: #ebeef5;
    cursor: not-allowed;
}

/* 分页信息 */
.pagination-info {
    color: #909399;
    font-size: 13px;
}

/* 页面头部样式 */
.page-header {
    margin-bottom: 20px;
}

.page-title {
    font-size: 20px;
    font-weight: 500;
    color: #303133;
    margin: 0;
}

/* 表格头部样式 */
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #fafafa;
}

.table-title {
    font-size: 16px;
    font-weight: 500;
    color: #303133;
}

.table-actions {
    display: flex;
    gap: 8px;
}

.table-actions .btn {
    margin: 0;
}

/* 操作按钮样式 */
.action-btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    margin-right: 4px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.action-btn-edit {
    color: #409eff;
    border: 1px solid #b3d8ff;
    background-color: #ecf5ff;
}

.action-btn-edit:hover {
    background-color: #409eff;
    color: #fff;
    text-decoration: none;
}

.action-btn-delete {
    color: #f56c6c;
    border: 1px solid #fbc4c4;
    background-color: #fef0f0;
}

.action-btn-delete:hover {
    background-color: #f56c6c;
    color: #fff;
    text-decoration: none;
}

/* 提示框样式 */
.alert {
    border-radius: 0.5rem;
    border: none;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
}

.alert-warning {
    background-color: #fff3cd;
    color: #664d03;
}

.alert-info {
    background-color: #d1ecf1;
    color: #055160;
}

/* 响应式样式 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 0.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
    
    .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
}

/* 工具提示样式 */
[title] {
    cursor: help;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0d6efd;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 文本截断 */
.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 状态标签 */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* 图标样式 */
.bi {
    vertical-align: -0.125em;
}

/* 自定义滚动条 */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 操作按钮组 */
.btn-group-sm > .btn, .btn-sm {
    margin-right: 0.25rem;
}

.btn-group-sm > .btn:last-child, .btn-sm:last-child {
    margin-right: 0;
}

/* 表单验证样式 */
.is-invalid {
    border-color: #dc3545;
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #dc3545;
}

/* 成功状态 */
.is-valid {
    border-color: #67c23a;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875em;
    color: #67c23a;
}

/* 空状态优化 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #909399;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.empty-state p {
    font-size: 14px;
    margin: 0;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #409eff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }

    .query-section {
        padding: 15px;
    }

    .query-form .row > div {
        margin-bottom: 12px;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .table-actions {
        flex-wrap: wrap;
        gap: 6px;
    }

    .table-responsive {
        font-size: 13px;
    }

    .table th,
    .table td {
        padding: 8px 6px;
    }

    .pagination-container {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .action-btn {
        font-size: 11px;
        padding: 2px 6px;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 表格行号样式 */
.table tbody tr td:first-child {
    font-weight: 500;
    color: #909399;
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-active {
    background-color: #67c23a;
}

.status-inactive {
    background-color: #f56c6c;
}

.status-pending {
    background-color: #e6a23c;
}
