// 全局变量
let currentPage = 1;
let pageSize = 10;
let editModal;
let importModal;

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    editModal = new bootstrap.Modal(document.getElementById('editModal'));
    importModal = new bootstrap.Modal(document.getElementById('importModal'));
});

// 查询数据
function queryData(page = 1) {
    currentPage = page;
    
    const formData = new FormData(document.getElementById('queryForm'));
    const params = new URLSearchParams();
    
    // 添加查询参数
    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            params.append(key, value);
        }
    }
    params.append('current', currentPage);
    params.append('size', pageSize);
    
    fetch(`/api/i18n/page?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                renderTable(data.data.records);
                renderPagination(data.data);
            } else {
                showAlert('查询失败：' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('查询失败:', error);
            showAlert('查询失败：网络错误', 'danger');
        });
}

// 渲染表格
function renderTable(records) {
    const tbody = document.getElementById('dataTableBody');
    tbody.innerHTML = '';
    
    if (!records || records.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }
    
    records.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="checkbox" class="row-checkbox" value="${record.id}"></td>
            <td>${record.id}</td>
            <td>${record.applicationName || ''}</td>
            <td>${record.languageTag || ''}</td>
            <td>${record.code || ''}</td>
            <td title="${record.message || ''}">${truncateText(record.message || '', 30)}</td>
            <td title="${record.remark || ''}">${truncateText(record.remark || '', 20)}</td>
            <td>${formatDateTime(record.createTime)}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="editData(${record.id})" title="编辑">
                    <i class="bi bi-pencil"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteData(${record.id})" title="删除">
                    <i class="bi bi-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 渲染分页
function renderPagination(pageData) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    const totalPages = pageData.pages;
    const current = pageData.current;
    
    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${current <= 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="queryData(${current - 1})">上一页</a>`;
    pagination.appendChild(prevLi);
    
    // 页码
    const startPage = Math.max(1, current - 2);
    const endPage = Math.min(totalPages, current + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === current ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="queryData(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${current >= totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="queryData(${current + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
    
    // 显示统计信息
    const info = document.createElement('li');
    info.className = 'page-item disabled';
    info.innerHTML = `<span class="page-link">共 ${pageData.total} 条记录，第 ${current}/${totalPages} 页</span>`;
    pagination.appendChild(info);
}

// 重置查询
function resetQuery() {
    document.getElementById('queryForm').reset();
    queryData();
}

// 显示新增模态框
function showAddModal() {
    document.getElementById('editModalTitle').textContent = '新增国际化信息';
    document.getElementById('editForm').reset();
    document.getElementById('editId').value = '';
    editModal.show();
}

// 编辑数据
function editData(id) {
    fetch(`/api/i18n/${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.code === 200) {
                const record = data.data;
                document.getElementById('editModalTitle').textContent = '编辑国际化信息';
                document.getElementById('editId').value = record.id;
                document.getElementById('editApplicationName').value = record.applicationName || '';
                document.getElementById('editLanguageTag').value = record.languageTag || '';
                document.getElementById('editCode').value = record.code || '';
                document.getElementById('editMessage').value = record.message || '';
                document.getElementById('editRemark').value = record.remark || '';
                editModal.show();
            } else {
                showAlert('获取数据失败：' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('获取数据失败:', error);
            showAlert('获取数据失败：网络错误', 'danger');
        });
}

// 保存数据
function saveData() {
    const form = document.getElementById('editForm');
    const formData = new FormData(form);
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }
    
    const id = data.id;
    const url = id ? `/api/i18n/${id}` : '/api/i18n';
    const method = id ? 'PUT' : 'POST';
    
    // 删除id字段，避免传递给后端
    delete data.id;
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showAlert(data.message || '保存成功', 'success');
            editModal.hide();
            queryData(currentPage);
        } else {
            showAlert('保存失败：' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('保存失败:', error);
        showAlert('保存失败：网络错误', 'danger');
    });
}

// 删除数据
function deleteData(id) {
    if (!confirm('确定要删除这条记录吗？')) {
        return;
    }
    
    fetch(`/api/i18n/${id}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showAlert(data.message || '删除成功', 'success');
            queryData(currentPage);
        } else {
            showAlert('删除失败：' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('删除失败:', error);
        showAlert('删除失败：网络错误', 'danger');
    });
}

// 批量删除
function batchDelete() {
    const checkboxes = document.querySelectorAll('.row-checkbox:checked');
    if (checkboxes.length === 0) {
        showAlert('请选择要删除的记录', 'warning');
        return;
    }
    
    if (!confirm(`确定要删除选中的 ${checkboxes.length} 条记录吗？`)) {
        return;
    }
    
    const ids = Array.from(checkboxes).map(cb => parseInt(cb.value));
    
    fetch('/api/i18n/batch', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(ids)
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showAlert(data.message || '批量删除成功', 'success');
            queryData(currentPage);
            document.getElementById('selectAll').checked = false;
        } else {
            showAlert('批量删除失败：' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('批量删除失败:', error);
        showAlert('批量删除失败：网络错误', 'danger');
    });
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.row-checkbox');
    
    checkboxes.forEach(cb => {
        cb.checked = selectAll.checked;
    });
}

// 导出数据
function exportData() {
    const formData = new FormData(document.getElementById('queryForm'));
    const data = {};
    
    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            data[key] = value;
        }
    }
    
    fetch('/api/i18n/export', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (response.ok) {
            return response.blob();
        }
        throw new Error('导出失败');
    })
    .then(blob => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `i18n_data_${new Date().getTime()}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        showAlert('导出成功', 'success');
    })
    .catch(error => {
        console.error('导出失败:', error);
        showAlert('导出失败：' + error.message, 'danger');
    });
}

// 显示导入模态框
function showImportModal() {
    document.getElementById('importForm').reset();
    importModal.show();
}

// 导入数据
function importData() {
    const fileInput = document.getElementById('importFile');
    if (!fileInput.files.length) {
        showAlert('请选择要导入的文件', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    
    fetch('/api/i18n/import', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showAlert('导入成功：' + data.data, 'success');
            importModal.hide();
            queryData(currentPage);
        } else {
            showAlert('导入失败：' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('导入失败:', error);
        showAlert('导入失败：网络错误', 'danger');
    });
}

// 刷新缓存
function refreshCache() {
    if (!confirm('确定要刷新所有缓存吗？')) {
        return;
    }
    
    fetch('/api/i18n/cache/refresh', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.code === 200) {
            showAlert(data.message || '缓存刷新成功', 'success');
        } else {
            showAlert('缓存刷新失败：' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('缓存刷新失败:', error);
        showAlert('缓存刷新失败：网络错误', 'danger');
    });
}

// 显示提示信息
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // 3秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// 截断文本
function truncateText(text, maxLength) {
    if (!text) return '';
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
}

// 格式化日期时间
function formatDateTime(dateTime) {
    if (!dateTime) return '';
    const date = new Date(dateTime);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}
