<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存管理 - 国际化管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/i18n.css}">
</head>
<body>
    <div class="container-fluid">
        <!-- 头部导航 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="bi bi-speedometer2"></i> 缓存管理
                </a>
                <div class="navbar-nav ms-auto">
                    <a class="nav-link" th:href="@{/i18n}">
                        <i class="bi bi-arrow-left"></i> 返回主页
                    </a>
                </div>
            </div>
        </nav>

        <!-- 缓存统计信息 -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up"></i> 本地缓存统计
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h3 class="text-primary" id="localCacheSize">-</h3>
                                    <p class="text-muted mb-0">缓存条目数</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h3 class="text-success" id="localHitRate">-</h3>
                                    <p class="text-muted mb-0">命中率</p>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="d-grid">
                            <button class="btn btn-outline-primary" onclick="refreshLocalCache()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新本地缓存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-server"></i> Redis缓存管理
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h3 class="text-info" id="redisKeyCount">-</h3>
                                    <p class="text-muted mb-0">Redis键数量</p>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h3 class="text-warning" id="redisMemory">-</h3>
                                    <p class="text-muted mb-0">内存使用</p>
                                </div>
                            </div>
                        </div>
                        <hr>
                        <div class="d-grid">
                            <button class="btn btn-outline-danger" onclick="clearRedisCache()">
                                <i class="bi bi-trash"></i> 清空Redis缓存
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 缓存操作 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-tools"></i> 缓存操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-4">
                        <label for="applicationName" class="form-label">应用名称</label>
                        <input type="text" class="form-control" id="applicationName" placeholder="留空表示所有应用">
                    </div>
                    <div class="col-md-4">
                        <label for="languageTag" class="form-label">语言标签</label>
                        <input type="text" class="form-control" id="languageTag" placeholder="如：zh-CN">
                    </div>
                    <div class="col-md-4">
                        <label for="code" class="form-label">国际化标识</label>
                        <input type="text" class="form-control" id="code" placeholder="如：menu.account">
                    </div>
                    <div class="col-12">
                        <button type="button" class="btn btn-primary" onclick="refreshSpecificCache()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新指定缓存
                        </button>
                        <button type="button" class="btn btn-warning" onclick="preloadCache()">
                            <i class="bi bi-download"></i> 预加载缓存
                        </button>
                        <button type="button" class="btn btn-danger" onclick="clearAllCache()">
                            <i class="bi bi-trash"></i> 清空所有缓存
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 缓存详细信息 -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i> 缓存详细信息
                </h5>
                <button class="btn btn-sm btn-outline-primary" onclick="loadCacheStats()">
                    <i class="bi bi-arrow-clockwise"></i> 刷新
                </button>
            </div>
            <div class="card-body">
                <pre id="cacheDetails" class="bg-light p-3 rounded">加载中...</pre>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadCacheStats();
            // 每30秒自动刷新一次统计信息
            setInterval(loadCacheStats, 30000);
        });

        // 加载缓存统计信息
        function loadCacheStats() {
            fetch('/api/i18n/cache/stats')
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        updateCacheStats(data.data);
                    } else {
                        showAlert('获取缓存统计失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('获取缓存统计失败:', error);
                    showAlert('获取缓存统计失败：网络错误', 'danger');
                });
        }

        // 更新缓存统计显示
        function updateCacheStats(stats) {
            document.getElementById('cacheDetails').textContent = stats;
            
            // 解析统计信息（这里简化处理，实际可能需要更复杂的解析）
            const lines = stats.split('\n');
            lines.forEach(line => {
                if (line.includes('size:')) {
                    const size = line.match(/size: (\d+)/);
                    if (size) {
                        document.getElementById('localCacheSize').textContent = size[1];
                    }
                }
                if (line.includes('hit rate:')) {
                    const hitRate = line.match(/hit rate: ([\d.]+)%/);
                    if (hitRate) {
                        document.getElementById('localHitRate').textContent = hitRate[1] + '%';
                    }
                }
            });
        }

        // 刷新本地缓存
        function refreshLocalCache() {
            const applicationName = document.getElementById('applicationName').value.trim();
            const url = applicationName ? 
                `/api/i18n/cache/refresh?applicationName=${encodeURIComponent(applicationName)}` : 
                '/api/i18n/cache/refresh';
            
            fetch(url, { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showAlert(data.message || '本地缓存刷新成功', 'success');
                        loadCacheStats();
                    } else {
                        showAlert('本地缓存刷新失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('本地缓存刷新失败:', error);
                    showAlert('本地缓存刷新失败：网络错误', 'danger');
                });
        }

        // 清空Redis缓存
        function clearRedisCache() {
            if (!confirm('确定要清空Redis缓存吗？此操作不可恢复！')) {
                return;
            }
            
            fetch('/api/i18n/cache/refresh', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showAlert('Redis缓存清空成功', 'success');
                        loadCacheStats();
                    } else {
                        showAlert('Redis缓存清空失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Redis缓存清空失败:', error);
                    showAlert('Redis缓存清空失败：网络错误', 'danger');
                });
        }

        // 刷新指定缓存
        function refreshSpecificCache() {
            const applicationName = document.getElementById('applicationName').value.trim();
            refreshLocalCache();
        }

        // 预加载缓存
        function preloadCache() {
            showAlert('预加载功能开发中...', 'info');
        }

        // 清空所有缓存
        function clearAllCache() {
            if (!confirm('确定要清空所有缓存吗？此操作不可恢复！')) {
                return;
            }
            
            fetch('/api/i18n/cache/refresh', { method: 'POST' })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        showAlert('所有缓存清空成功', 'success');
                        loadCacheStats();
                    } else {
                        showAlert('清空缓存失败：' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('清空缓存失败:', error);
                    showAlert('清空缓存失败：网络错误', 'danger');
                });
        }

        // 显示提示信息
        function showAlert(message, type = 'info') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        }
    </script>
</body>
</html>
