<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>国际化管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" th:href="@{/css/i18n.css}">
</head>
<body>
    <div class="container-fluid">
        <!-- 头部标题 -->
        <div class="page-header mb-4">
            <h2 class="page-title">国际化信息</h2>
        </div>

        <!-- 查询条件 -->
        <div class="query-section">
            <form id="queryForm" class="query-form">
                <div class="row">
                    <div class="col-md-3">
                        <span>语言标签:</span>
                        <input type="text" class="form-control" id="languageTag" name="languageTag" placeholder="请输入">
                    </div>
                    <div class="col-md-3">
                        <span>国际化标识:</span>
                        <input type="text" class="form-control" id="code" name="code" placeholder="请输入">
                    </div>
                    <div class="col-md-3">
                        <span>文本值:</span>
                        <input type="text" class="form-control" id="message" name="message" placeholder="请输入">
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary" onclick="queryData()">查询</button>
                        <button type="button" class="btn btn-secondary" onclick="resetQuery()">重置</button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 数据表格 -->
        <div class="table-container">
            <!-- 操作按钮栏 -->
            <div class="table-header">
                <div class="table-title">国际化信息</div>
                <div class="table-actions">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="exportData()">
                        <i class="bi bi-download"></i> 导出
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="showImportModal()">
                        <i class="bi bi-upload"></i> 导入
                    </button>
                    <button type="button" class="btn btn-primary btn-sm" onclick="showAddModal()">
                        <i class="bi bi-plus"></i> 新增
                    </button>
                    <button type="button" class="btn btn-secondary btn-sm" onclick="resetQuery()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新
                    </button>
                </div>
            </div>

            <!-- 表格内容 -->
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th width="50">ID</th>
                            <th>语言标签</th>
                            <th>国际化标识</th>
                            <th>文本值</th>
                            <th>备注</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="dataTableBody">
                        <!-- 数据行将通过JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination-container">
                <div class="pagination-info" id="paginationInfo">
                    第 1-10 条/共 58 条
                </div>
                <nav aria-label="分页导航">
                    <ul class="pagination" id="pagination">
                        <!-- 分页按钮将通过JavaScript动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- 新增/编辑模态框 -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalTitle">新增国际化信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="editForm">
                        <input type="hidden" id="editId" name="id">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="editApplicationName" class="form-label">应用名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="editApplicationName" name="applicationName" required>
                            </div>
                            <div class="col-md-6">
                                <label for="editLanguageTag" class="form-label">语言标签 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="editLanguageTag" name="languageTag" required placeholder="如：zh-CN, en-US">
                            </div>
                            <div class="col-12">
                                <label for="editCode" class="form-label">国际化标识 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="editCode" name="code" required placeholder="如：menu.account.settings">
                            </div>
                            <div class="col-12">
                                <label for="editMessage" class="form-label">文本内容</label>
                                <textarea class="form-control" id="editMessage" name="message" rows="3" placeholder="支持占位符 {0}, {1}"></textarea>
                            </div>
                            <div class="col-12">
                                <label for="editRemark" class="form-label">备注</label>
                                <textarea class="form-control" id="editRemark" name="remark" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveData()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入模态框 -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">导入国际化数据</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="importForm" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="importFile" class="form-label">选择Excel文件</label>
                            <input type="file" class="form-control" id="importFile" name="file" accept=".xlsx,.xls" required>
                        </div>
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>导入说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li>支持.xlsx和.xls格式的Excel文件</li>
                                <li>第一行必须是标题行：应用名称、语言标签、国际化标识、文本内容、备注</li>
                                <li>应用名称、语言标签、国际化标识为必填字段</li>
                                <li>如果数据已存在，将会更新现有记录</li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="importData()">导入</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script th:src="@{/js/i18n.js}"></script>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            queryData();
        });
    </script>
</body>
</html>
