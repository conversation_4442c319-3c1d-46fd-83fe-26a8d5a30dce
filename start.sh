#!/bin/bash

# Trina Admin I18n 启动脚本

echo "=========================================="
echo "  Trina Admin I18n 国际化管理系统"
echo "=========================================="

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "错误: 未找到Java环境，请安装JDK 17或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven环境，请安装Maven 3.6或更高版本"
    exit 1
fi

# 检查MySQL连接
echo "检查MySQL连接..."
mysql -h localhost -u root -p -e "SELECT 1;" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 无法连接到MySQL，请确保MySQL服务已启动并配置正确"
fi

# 检查Redis连接
echo "检查Redis连接..."
redis-cli ping 2>/dev/null
if [ $? -ne 0 ]; then
    echo "警告: 无法连接到Redis，请确保Redis服务已启动"
fi

# 编译项目
echo "编译项目..."
mvn clean compile -q
if [ $? -ne 0 ]; then
    echo "错误: 项目编译失败"
    exit 1
fi

# 启动应用
echo "启动应用..."
echo "访问地址："
echo "  管理界面: http://localhost:8080/i18n"
echo "  缓存管理: http://localhost:8080/i18n/cache"
echo "  API接口: http://localhost:8080/api/i18n"
echo "=========================================="

mvn spring-boot:run
